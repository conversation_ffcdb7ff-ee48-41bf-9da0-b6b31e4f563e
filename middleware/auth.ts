export default defineNuxtRouteMiddleware((to, from) => {
  // 只在客户端执行
  if (typeof window === 'undefined') return

  const token = localStorage.getItem('auth_token')
  
  if (!token) {
    // 没有token，重定向到登录页
    return navigateTo('/login')
  }

  // 可以在这里添加更多的token验证逻辑
  // 比如检查token是否过期等
  try {
    // 简单的token格式检查
    const parts = token.split('.')
    if (parts.length !== 3) {
      // token格式不正确
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
      return navigateTo('/login')
    }

    // 检查token是否过期（可选）
    const payload = JSON.parse(atob(parts[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    
    if (payload.exp && payload.exp < currentTime) {
      // token已过期
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
      return navigateTo('/login')
    }
  } catch (error) {
    // token解析失败
    console.error('Token解析失败:', error)
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
    return navigateTo('/login')
  }
})
