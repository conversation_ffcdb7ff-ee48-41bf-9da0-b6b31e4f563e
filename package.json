{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "bcrypt": "^6.0.0", "jsonwebtoken": "^9.0.2", "nuxt": "^3.17.6", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad", "devDependencies": {"@nuxtjs/tailwindcss": "^6.14.0"}}