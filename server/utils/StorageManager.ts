import bcrypt from 'bcrypt'

export interface User {
  id: string
  username: string
  email: string
  password: string
  role: 'user' | 'admin'
  createdAt: Date
}

export interface CreateUserData {
  username: string
  email: string
  password: string
  role?: 'user' | 'admin'
}

export class StorageManager {
  private users: Map<string, User> = new Map()
  private usersByUsername: Map<string, User> = new Map()
  private usersByEmail: Map<string, User> = new Map()

  constructor() {
    // 初始化一个管理员用户
    this.createUser({
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin'
    })
  }

  // 创建用户
  async createUser(userData: CreateUserData): Promise<User> {
    // 检查用户名是否已存在
    if (this.usersByUsername.has(userData.username)) {
      throw new Error('用户名已存在')
    }

    // 检查邮箱是否已存在
    if (this.usersByEmail.has(userData.email)) {
      throw new Error('邮箱已存在')
    }

    // 生成用户ID
    const id = this.generateId()

    // 加密密码
    const hashedPassword = await bcrypt.hash(userData.password, 10)

    // 创建用户对象
    const user: User = {
      id,
      username: userData.username,
      email: userData.email,
      password: hashedPassword,
      role: userData.role || 'user',
      createdAt: new Date()
    }

    // 存储用户
    this.users.set(id, user)
    this.usersByUsername.set(userData.username, user)
    this.usersByEmail.set(userData.email, user)

    return user
  }

  // 验证用户登录
  async validateUser(username: string, password: string): Promise<User | null> {
    const user = this.usersByUsername.get(username)
    if (!user) {
      return null
    }

    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      return null
    }

    return user
  }

  // 根据ID获取用户
  getUserById(id: string): User | null {
    return this.users.get(id) || null
  }

  // 根据用户名获取用户
  getUserByUsername(username: string): User | null {
    return this.usersByUsername.get(username) || null
  }

  // 根据邮箱获取用户
  getUserByEmail(email: string): User | null {
    return this.usersByEmail.get(email) || null
  }

  // 获取所有用户
  getAllUsers(): User[] {
    return Array.from(this.users.values())
  }

  // 删除用户
  deleteUser(id: string): boolean {
    const user = this.users.get(id)
    if (!user) {
      return false
    }

    this.users.delete(id)
    this.usersByUsername.delete(user.username)
    this.usersByEmail.delete(user.email)

    return true
  }

  // 更新用户
  async updateUser(id: string, updateData: Partial<CreateUserData>): Promise<User | null> {
    const user = this.users.get(id)
    if (!user) {
      return null
    }

    // 检查用户名冲突
    if (updateData.username && updateData.username !== user.username) {
      if (this.usersByUsername.has(updateData.username)) {
        throw new Error('用户名已存在')
      }
    }

    // 检查邮箱冲突
    if (updateData.email && updateData.email !== user.email) {
      if (this.usersByEmail.has(updateData.email)) {
        throw new Error('邮箱已存在')
      }
    }

    // 更新用户数据
    const updatedUser: User = { ...user }

    if (updateData.username) {
      this.usersByUsername.delete(user.username)
      updatedUser.username = updateData.username
      this.usersByUsername.set(updateData.username, updatedUser)
    }

    if (updateData.email) {
      this.usersByEmail.delete(user.email)
      updatedUser.email = updateData.email
      this.usersByEmail.set(updateData.email, updatedUser)
    }

    if (updateData.password) {
      updatedUser.password = await bcrypt.hash(updateData.password, 10)
    }

    if (updateData.role) {
      updatedUser.role = updateData.role
    }

    this.users.set(id, updatedUser)

    return updatedUser
  }

  // 生成唯一ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 获取用户统计信息
  getStats() {
    const totalUsers = this.users.size
    const adminUsers = Array.from(this.users.values()).filter(user => user.role === 'admin').length
    const regularUsers = totalUsers - adminUsers

    return {
      totalUsers,
      adminUsers,
      regularUsers
    }
  }
}

// 创建全局实例
export const storageManager = new StorageManager()
