import { UserController } from '../../controllers/UserController'
import type { RegisterData } from '../../models/UserModel'

export default defineEventHandler(async (event) => {
  try {
    // 只允许POST请求
    if (getMethod(event) !== 'POST') {
      throw createError({
        statusCode: 405,
        statusMessage: 'Method Not Allowed'
      })
    }

    // 获取请求体
    const body = await readBody(event) as RegisterData

    // 验证必需字段
    if (!body.username || !body.email || !body.password || !body.confirmPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields'
      })
    }

    // 调用控制器处理注册
    const result = await UserController.register(body)

    // 设置响应状态码
    if (!result.success) {
      setResponseStatus(event, 400)
    }

    return result
  } catch (error) {
    console.error('注册API错误:', error)
    
    // 如果是已知错误，直接抛出
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error'
    })
  }
})
