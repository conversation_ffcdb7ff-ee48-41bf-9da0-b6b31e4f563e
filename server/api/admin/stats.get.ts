import { UserController } from '../../controllers/UserController'
import { JwtUtils } from '../../utils/JwtUtils'

export default defineEventHandler(async (event) => {
  try {
    // 只允许GET请求
    if (getMethod(event) !== 'GET') {
      throw createError({
        statusCode: 405,
        statusMessage: 'Method Not Allowed'
      })
    }

    // 获取Authorization头
    const authHeader = getHeader(event, 'authorization')
    const token = JwtUtils.extractTokenFromHeader(authHeader)

    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing authorization token'
      })
    }

    // 调用控制器获取统计信息
    const result = UserController.getStats(token)

    // 设置响应状态码
    if (!result.success) {
      if (result.message.includes('权限不足')) {
        setResponseStatus(event, 403)
      } else {
        setResponseStatus(event, 401)
      }
    }

    return result
  } catch (error) {
    console.error('获取统计信息API错误:', error)
    
    // 如果是已知错误，直接抛出
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error'
    })
  }
})
