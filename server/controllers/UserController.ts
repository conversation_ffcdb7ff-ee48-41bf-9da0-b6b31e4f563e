import { UserModel, type LoginCredentials, type RegisterData, type UserResponse } from '../models/UserModel'
import { JwtUtils } from '../utils/JwtUtils'

export interface LoginResponse {
  success: boolean
  message: string
  token?: string
  user?: UserResponse
}

export interface RegisterResponse {
  success: boolean
  message: string
  user?: UserResponse
}

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

export class UserController {
  // 用户注册
  static async register(data: RegisterData): Promise<RegisterResponse> {
    try {
      const user = await UserModel.register(data)
      
      return {
        success: true,
        message: '注册成功',
        user
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '注册失败'
      }
    }
  }

  // 用户登录
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const user = await UserModel.login(credentials)
      
      if (!user) {
        return {
          success: false,
          message: '用户名或密码错误'
        }
      }

      // 生成JWT token
      const token = JwtUtils.generateToken(user)
      
      // 转换为响应格式（移除密码）
      const userResponse: UserResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt
      }

      return {
        success: true,
        message: '登录成功',
        token,
        user: userResponse
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '登录失败'
      }
    }
  }

  // 获取当前用户信息
  static getCurrentUser(token: string): ApiResponse<UserResponse> {
    try {
      const payload = JwtUtils.verifyToken(token)
      
      if (!payload) {
        return {
          success: false,
          message: 'Token无效或已过期'
        }
      }

      const user = UserModel.getUserById(payload.userId)
      
      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        }
      }

      return {
        success: true,
        message: '获取用户信息成功',
        data: user
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '获取用户信息失败'
      }
    }
  }

  // 获取所有用户（管理员功能）
  static getAllUsers(token: string): ApiResponse<UserResponse[]> {
    try {
      // 验证管理员权限
      if (!JwtUtils.hasPermission(token, 'admin')) {
        return {
          success: false,
          message: '权限不足，需要管理员权限'
        }
      }

      const users = UserModel.getAllUsers()
      
      return {
        success: true,
        message: '获取用户列表成功',
        data: users
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '获取用户列表失败'
      }
    }
  }

  // 删除用户（管理员功能）
  static deleteUser(userId: string, token: string): ApiResponse<boolean> {
    try {
      // 验证管理员权限
      if (!JwtUtils.hasPermission(token, 'admin')) {
        return {
          success: false,
          message: '权限不足，需要管理员权限'
        }
      }

      // 获取当前用户信息
      const payload = JwtUtils.verifyToken(token)
      if (!payload) {
        return {
          success: false,
          message: 'Token无效'
        }
      }

      // 防止删除自己
      if (payload.userId === userId) {
        return {
          success: false,
          message: '不能删除自己的账户'
        }
      }

      const result = UserModel.deleteUser(userId)
      
      if (!result) {
        return {
          success: false,
          message: '用户不存在或删除失败'
        }
      }

      return {
        success: true,
        message: '删除用户成功',
        data: true
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '删除用户失败'
      }
    }
  }

  // 获取用户统计信息（管理员功能）
  static getStats(token: string): ApiResponse<any> {
    try {
      // 验证管理员权限
      if (!JwtUtils.hasPermission(token, 'admin')) {
        return {
          success: false,
          message: '权限不足，需要管理员权限'
        }
      }

      const stats = UserModel.getStats()
      
      return {
        success: true,
        message: '获取统计信息成功',
        data: stats
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '获取统计信息失败'
      }
    }
  }

  // 验证token
  static verifyToken(token: string): ApiResponse<any> {
    try {
      const payload = JwtUtils.verifyToken(token)
      
      if (!payload) {
        return {
          success: false,
          message: 'Token无效或已过期'
        }
      }

      return {
        success: true,
        message: 'Token有效',
        data: {
          userId: payload.userId,
          username: payload.username,
          role: payload.role,
          remainingTime: JwtUtils.getTokenRemainingTime(token)
        }
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Token验证失败'
      }
    }
  }
}
