import { storageManager, type User, type CreateUserData } from '../utils/StorageManager'

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
}

export interface UserResponse {
  id: string
  username: string
  email: string
  role: 'user' | 'admin'
  createdAt: Date
}

export class UserModel {
  // 用户注册
  static async register(data: RegisterData): Promise<UserResponse> {
    // 验证输入数据
    this.validateRegisterData(data)

    // 检查密码确认
    if (data.password !== data.confirmPassword) {
      throw new Error('密码确认不匹配')
    }

    // 创建用户数据
    const createUserData: CreateUserData = {
      username: data.username,
      email: data.email,
      password: data.password,
      role: 'user'
    }

    try {
      const user = await storageManager.createUser(createUserData)
      return this.toUserResponse(user)
    } catch (error) {
      throw error
    }
  }

  // 用户登录验证
  static async login(credentials: LoginCredentials): Promise<User | null> {
    this.validateLoginCredentials(credentials)

    try {
      const user = await storageManager.validateUser(credentials.username, credentials.password)
      return user
    } catch (error) {
      console.error('登录验证失败:', error)
      return null
    }
  }

  // 根据ID获取用户
  static getUserById(id: string): UserResponse | null {
    const user = storageManager.getUserById(id)
    return user ? this.toUserResponse(user) : null
  }

  // 根据用户名获取用户
  static getUserByUsername(username: string): UserResponse | null {
    const user = storageManager.getUserByUsername(username)
    return user ? this.toUserResponse(user) : null
  }

  // 获取所有用户（管理员功能）
  static getAllUsers(): UserResponse[] {
    const users = storageManager.getAllUsers()
    return users.map(user => this.toUserResponse(user))
  }

  // 删除用户（管理员功能）
  static deleteUser(id: string): boolean {
    return storageManager.deleteUser(id)
  }

  // 更新用户信息
  static async updateUser(id: string, updateData: Partial<CreateUserData>): Promise<UserResponse | null> {
    try {
      const user = await storageManager.updateUser(id, updateData)
      return user ? this.toUserResponse(user) : null
    } catch (error) {
      throw error
    }
  }

  // 获取用户统计信息
  static getStats() {
    return storageManager.getStats()
  }

  // 验证注册数据
  private static validateRegisterData(data: RegisterData): void {
    if (!data.username || data.username.trim().length < 3) {
      throw new Error('用户名至少需要3个字符')
    }

    if (!data.email || !this.isValidEmail(data.email)) {
      throw new Error('请输入有效的邮箱地址')
    }

    if (!data.password || data.password.length < 6) {
      throw new Error('密码至少需要6个字符')
    }

    if (!data.confirmPassword) {
      throw new Error('请确认密码')
    }
  }

  // 验证登录凭据
  private static validateLoginCredentials(credentials: LoginCredentials): void {
    if (!credentials.username || credentials.username.trim().length === 0) {
      throw new Error('请输入用户名')
    }

    if (!credentials.password || credentials.password.length === 0) {
      throw new Error('请输入密码')
    }
  }

  // 验证邮箱格式
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 转换为用户响应对象（移除敏感信息）
  private static toUserResponse(user: User): UserResponse {
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt
    }
  }
}
