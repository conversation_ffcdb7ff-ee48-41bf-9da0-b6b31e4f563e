<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">管理员面板</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">
              欢迎，{{ currentUser?.username }}
            </span>
            <button
              @click="handleLogout"
              class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium"
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-bold">总</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.totalUsers || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-bold">管</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">管理员</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.adminUsers || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-bold">普</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">普通用户</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.regularUsers || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 class="text-lg leading-6 font-medium text-gray-900">用户列表</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">系统中所有注册用户的信息</p>
          </div>
          <button
            @click="loadData"
            :disabled="loading"
            class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
          >
            {{ loading ? '刷新中...' : '刷新' }}
          </button>
        </div>

        <div v-if="error" class="px-4 py-3 bg-red-50 border-l-4 border-red-400">
          <p class="text-sm text-red-700">{{ error }}</p>
        </div>

        <div v-if="loading && !users.length" class="px-4 py-8 text-center">
          <p class="text-gray-500">加载中...</p>
        </div>

        <ul v-else-if="users.length" class="divide-y divide-gray-200">
          <li v-for="user in users" :key="user.id" class="px-4 py-4 sm:px-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-700">
                      {{ user.username.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-gray-900">{{ user.username }}</p>
                    <span
                      :class="[
                        'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                        user.role === 'admin' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-blue-100 text-blue-800'
                      ]"
                    >
                      {{ user.role === 'admin' ? '管理员' : '普通用户' }}
                    </span>
                  </div>
                  <p class="text-sm text-gray-500">{{ user.email }}</p>
                  <p class="text-xs text-gray-400">
                    注册时间: {{ formatDate(user.createdAt) }}
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">ID: {{ user.id }}</span>
              </div>
            </div>
          </li>
        </ul>

        <div v-else class="px-4 py-8 text-center">
          <p class="text-gray-500">暂无用户数据</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface User {
  id: string
  username: string
  email: string
  role: 'user' | 'admin'
  createdAt: string
}

interface Stats {
  totalUsers: number
  adminUsers: number
  regularUsers: number
}

interface ApiResponse<T> {
  success: boolean
  message: string
  data?: T
}

// 页面元数据
definePageMeta({
  layout: false,
  middleware: ['auth']
})

// 响应式数据
const users = ref<User[]>([])
const stats = ref<Stats | null>(null)
const currentUser = ref<User | null>(null)
const loading = ref(false)
const error = ref('')

// 获取token
const getToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth_token')
  }
  return null
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 加载数据
const loadData = async () => {
  const token = getToken()
  if (!token) {
    navigateTo('/login')
    return
  }

  loading.value = true
  error.value = ''

  try {
    // 并行请求用户列表和统计信息
    const [usersResponse, statsResponse] = await Promise.all([
      $fetch<ApiResponse<User[]>>('/api/admin/users', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }),
      $fetch<ApiResponse<Stats>>('/api/admin/stats', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
    ])

    if (usersResponse.success && usersResponse.data) {
      users.value = usersResponse.data
    } else {
      throw new Error(usersResponse.message || '获取用户列表失败')
    }

    if (statsResponse.success && statsResponse.data) {
      stats.value = statsResponse.data
    }
  } catch (err: any) {
    console.error('加载数据错误:', err)
    
    if (err.status === 401 || err.status === 403) {
      error.value = '认证失败，请重新登录'
      setTimeout(() => {
        handleLogout()
      }, 2000)
    } else {
      error.value = err.data?.message || err.message || '加载数据失败'
    }
  } finally {
    loading.value = false
  }
}

// 退出登录
const handleLogout = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
  }
  navigateTo('/login')
}

// 页面加载时的处理
onMounted(async () => {
  if (typeof window !== 'undefined') {
    const token = getToken()
    if (!token) {
      navigateTo('/login')
      return
    }

    // 获取当前用户信息
    const userInfo = localStorage.getItem('user_info')
    if (userInfo) {
      try {
        currentUser.value = JSON.parse(userInfo)
      } catch (e) {
        console.error('解析用户信息失败:', e)
      }
    }

    // 加载数据
    await loadData()
  }
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
