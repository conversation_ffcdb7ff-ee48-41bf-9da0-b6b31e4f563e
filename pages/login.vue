<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          用户登录
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          还没有账户？
          <NuxtLink to="/register" class="font-medium text-indigo-600 hover:text-indigo-500">
            立即注册
          </NuxtLink>
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="username" class="sr-only">用户名</label>
            <input
              id="username"
              v-model="form.username"
              name="username"
              type="text"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              placeholder="用户名"
            />
          </div>
          <div>
            <label for="password" class="sr-only">密码</label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              placeholder="密码"
            />
          </div>
        </div>

        <div v-if="error" class="text-red-600 text-sm text-center">
          {{ error }}
        </div>

        <div v-if="success" class="text-green-600 text-sm text-center">
          {{ success }}
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loading">登录中...</span>
            <span v-else>登录</span>
          </button>
        </div>

        <div class="text-center">
          <NuxtLink to="/" class="text-indigo-600 hover:text-indigo-500 text-sm">
            返回首页
          </NuxtLink>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
interface LoginForm {
  username: string
  password: string
}

interface LoginResponse {
  success: boolean
  message: string
  token?: string
  user?: any
}

// 页面元数据
definePageMeta({
  layout: false
})

// 响应式数据
const form = ref<LoginForm>({
  username: '',
  password: ''
})

const loading = ref(false)
const error = ref('')
const success = ref('')

// 登录处理函数
const handleLogin = async () => {
  if (loading.value) return

  // 清除之前的消息
  error.value = ''
  success.value = ''

  // 验证表单
  if (!form.value.username.trim()) {
    error.value = '请输入用户名'
    return
  }

  if (!form.value.password) {
    error.value = '请输入密码'
    return
  }

  loading.value = true

  try {
    const { data } = await $fetch<LoginResponse>('/api/auth/login', {
      method: 'POST',
      body: {
        username: form.value.username.trim(),
        password: form.value.password
      }
    })

    if (data.success && data.token) {
      success.value = '登录成功，正在跳转...'
      
      // 存储token到localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('auth_token', data.token)
        localStorage.setItem('user_info', JSON.stringify(data.user))
      }

      // 延迟跳转，让用户看到成功消息
      setTimeout(() => {
        navigateTo('/admin')
      }, 1000)
    } else {
      error.value = data.message || '登录失败'
    }
  } catch (err: any) {
    console.error('登录错误:', err)
    
    if (err.data && err.data.message) {
      error.value = err.data.message
    } else if (err.statusMessage) {
      error.value = err.statusMessage
    } else {
      error.value = '登录失败，请稍后重试'
    }
  } finally {
    loading.value = false
  }
}

// 检查是否已登录
onMounted(() => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('auth_token')
    if (token) {
      // 如果已登录，直接跳转到管理页面
      navigateTo('/admin')
    }
  }
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
