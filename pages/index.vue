<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h1 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        用户管理系统
      </h1>
      <p class="mt-2 text-center text-sm text-gray-600">
        基于JWT认证的用户登录和管理系统
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div class="space-y-4">
          <div v-if="isLoggedIn" class="text-center">
            <p class="text-sm text-gray-600 mb-4">
              欢迎回来，{{ currentUser?.username }}！
            </p>
            <div class="space-y-3">
              <NuxtLink
                to="/admin"
                class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                进入管理面板
              </NuxtLink>
              <button
                @click="handleLogout"
                class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                退出登录
              </button>
            </div>
          </div>

          <div v-else class="space-y-3">
            <NuxtLink
              to="/login"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              用户登录
            </NuxtLink>
            <NuxtLink
              to="/register"
              class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              用户注册
            </NuxtLink>
          </div>
        </div>

        <div class="mt-8 border-t border-gray-200 pt-6">
          <div class="text-center">
            <h3 class="text-lg font-medium text-gray-900 mb-4">系统功能</h3>
            <div class="grid grid-cols-1 gap-3 text-sm text-gray-600">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                JWT身份认证
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                用户注册登录
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                管理员面板
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                用户列表管理
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                MVC架构设计
              </div>
            </div>
          </div>
        </div>

        <div class="mt-6 text-center text-xs text-gray-500">
          <p>默认管理员账户：admin / admin123</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface User {
  id: string
  username: string
  email: string
  role: 'user' | 'admin'
  createdAt: string
}

// 页面元数据
definePageMeta({
  layout: false
})

// 响应式数据
const isLoggedIn = ref(false)
const currentUser = ref<User | null>(null)

// 退出登录
const handleLogout = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
  }
  isLoggedIn.value = false
  currentUser.value = null
}

// 检查登录状态
onMounted(() => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('auth_token')
    const userInfo = localStorage.getItem('user_info')
    
    if (token && userInfo) {
      try {
        currentUser.value = JSON.parse(userInfo)
        isLoggedIn.value = true
      } catch (e) {
        console.error('解析用户信息失败:', e)
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_info')
      }
    }
  }
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
