<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          用户注册
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          已有账户？
          <NuxtLink to="/login" class="font-medium text-indigo-600 hover:text-indigo-500">
            立即登录
          </NuxtLink>
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleRegister">
        <div class="space-y-4">
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700">用户名</label>
            <input
              id="username"
              v-model="form.username"
              name="username"
              type="text"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="请输入用户名（至少3个字符）"
            />
          </div>
          
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">邮箱</label>
            <input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="请输入邮箱地址"
            />
          </div>
          
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">密码</label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="请输入密码（至少6个字符）"
            />
          </div>
          
          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700">确认密码</label>
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              name="confirmPassword"
              type="password"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="请再次输入密码"
            />
          </div>
        </div>

        <div v-if="error" class="text-red-600 text-sm text-center">
          {{ error }}
        </div>

        <div v-if="success" class="text-green-600 text-sm text-center">
          {{ success }}
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loading">注册中...</span>
            <span v-else>注册</span>
          </button>
        </div>

        <div class="text-center">
          <NuxtLink to="/" class="text-indigo-600 hover:text-indigo-500 text-sm">
            返回首页
          </NuxtLink>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
}

interface RegisterResponse {
  success: boolean
  message: string
  user?: any
}

// 页面元数据
definePageMeta({
  layout: false
})

// 响应式数据
const form = ref<RegisterForm>({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const loading = ref(false)
const error = ref('')
const success = ref('')

// 注册处理函数
const handleRegister = async () => {
  if (loading.value) return

  // 清除之前的消息
  error.value = ''
  success.value = ''

  // 验证表单
  if (!form.value.username.trim()) {
    error.value = '请输入用户名'
    return
  }

  if (form.value.username.trim().length < 3) {
    error.value = '用户名至少需要3个字符'
    return
  }

  if (!form.value.email.trim()) {
    error.value = '请输入邮箱地址'
    return
  }

  if (!form.value.password) {
    error.value = '请输入密码'
    return
  }

  if (form.value.password.length < 6) {
    error.value = '密码至少需要6个字符'
    return
  }

  if (!form.value.confirmPassword) {
    error.value = '请确认密码'
    return
  }

  if (form.value.password !== form.value.confirmPassword) {
    error.value = '两次输入的密码不一致'
    return
  }

  loading.value = true

  try {
    const { data } = await $fetch<RegisterResponse>('/api/auth/register', {
      method: 'POST',
      body: {
        username: form.value.username.trim(),
        email: form.value.email.trim(),
        password: form.value.password,
        confirmPassword: form.value.confirmPassword
      }
    })

    if (data.success) {
      success.value = '注册成功！正在跳转到登录页面...'
      
      // 清空表单
      form.value = {
        username: '',
        email: '',
        password: '',
        confirmPassword: ''
      }

      // 延迟跳转，让用户看到成功消息
      setTimeout(() => {
        navigateTo('/login')
      }, 2000)
    } else {
      error.value = data.message || '注册失败'
    }
  } catch (err: any) {
    console.error('注册错误:', err)
    
    if (err.data && err.data.message) {
      error.value = err.data.message
    } else if (err.statusMessage) {
      error.value = err.statusMessage
    } else {
      error.value = '注册失败，请稍后重试'
    }
  } finally {
    loading.value = false
  }
}

// 检查是否已登录
onMounted(() => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('auth_token')
    if (token) {
      // 如果已登录，直接跳转到管理页面
      navigateTo('/admin')
    }
  }
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
